@echo off
chcp 65001 >nul
echo ========================================
echo PPT翻译工具 - 完整版WPF界面
echo 包含术语库编辑功能
echo ========================================
echo.

REM 检查 .NET SDK
dotnet --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 未找到 .NET SDK
    echo 请先安装 .NET 8.0 SDK
    echo 下载地址: https://dotnet.microsoft.com/download
    pause
    exit /b 1
)

echo ✅ 检测到 .NET SDK 版本:
dotnet --version
echo.

echo 🔨 构建完整版WPF应用...
dotnet build src/PPTTranslator.UI/PPTTranslator.UI.csproj --configuration Release
if errorlevel 1 (
    echo ❌ 构建失败
    pause
    exit /b 1
)

echo.
echo ✅ 构建成功！
echo.
echo 🚀 启动PPT翻译工具 - 完整版界面...
echo.
echo 📋 功能说明:
echo   - 🎨 现代化WPF界面
echo   - 📁 PPT文件选择和处理
echo   - 📚 术语库编辑和管理
echo   - ⚙️ 翻译设置配置
echo   - 📊 实时翻译结果展示
echo   - 📈 处理进度跟踪
echo.
echo 💡 使用提示:
echo   - 点击"术语库管理"按钮可以编辑术语库
echo   - 支持添加、删除、修改术语条目
echo   - 可以导入/导出术语库文件
echo   - 支持术语分类和优先级设置
echo.

REM 启动应用
dotnet run --project src/PPTTranslator.UI/PPTTranslator.UI.csproj --configuration Release

echo.
echo 应用已关闭
pause
