using System;
using System.Windows;

namespace PPTTranslator.UI;

/// <summary>
/// 程序入口点
/// </summary>
public class Program
{
    [STAThread]
    public static void Main(string[] args)
    {
        try
        {
            var app = new App();
            app.InitializeComponent();
            app.Run();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"应用启动失败: {ex.Message}\n\n详细信息:\n{ex}", 
                          "错误", MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
}
