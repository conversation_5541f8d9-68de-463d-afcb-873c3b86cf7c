<Application x:Class="PPTTranslator.UI.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Material Design -->
                <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="Blue" SecondaryColor="Lime" />
                <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
                
                <!-- 自定义样式 -->
                <ResourceDictionary Source="Styles/CommonStyles.xaml" />
            </ResourceDictionary.MergedDictionaries>
            
            <!-- 全局资源 -->
            <SolidColorBrush x:Key="PrimaryBrush" Color="#2196F3"/>
            <SolidColorBrush x:Key="SecondaryBrush" Color="#FFC107"/>
            <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
            <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
            <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
            
            <!-- 字体大小 -->
            <system:Double x:Key="HeaderFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">24</system:Double>
            <system:Double x:Key="SubHeaderFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">18</system:Double>
            <system:Double x:Key="BodyFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">14</system:Double>
            <system:Double x:Key="CaptionFontSize" xmlns:system="clr-namespace:System;assembly=mscorlib">12</system:Double>
            
            <!-- 间距 -->
            <Thickness x:Key="DefaultMargin">8</Thickness>
            <Thickness x:Key="LargeMargin">16</Thickness>
            <Thickness x:Key="SmallMargin">4</Thickness>
            
        </ResourceDictionary>
    </Application.Resources>
</Application>
